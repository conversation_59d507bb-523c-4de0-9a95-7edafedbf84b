package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.domain.BlacklistedToken;
import com.neoproxy.pro.domain.RefreshToken;
import com.neoproxy.pro.domain.User;
import com.neoproxy.pro.repository.BlacklistedTokenRepository;
import com.neoproxy.pro.repository.RefreshTokenRepository;
import com.neoproxy.pro.service.BlacklistedTokenService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class BlacklistedTokenServiceImpl implements BlacklistedTokenService {
    
    BlacklistedTokenRepository blacklistedTokenRepository;
    RefreshTokenRepository refreshTokenRepository; // Thêm dependency này
    
    @Override
    public void blacklistToken(String token, User user, String reason) {
        String tokenHash = hashToken(token);
        
        if (!blacklistedTokenRepository.existsByTokenHash(tokenHash)) {
            BlacklistedToken blacklistedToken = BlacklistedToken.builder()
                    .tokenHash(tokenHash)
                    .user(user)
                    .reason(reason)
                    .build();
            
            blacklistedTokenRepository.save(blacklistedToken);
            log.info("Token blacklisted for user: {} with reason: {}", user.getEmail(), reason);
        }
    }
    
    @Override
    public boolean isTokenBlacklisted(String token) {
        String tokenHash = hashToken(token);
        return blacklistedTokenRepository.existsByTokenHash(tokenHash);
    }
    
    @Override
    public void blacklistAllUserTokens(User user, String reason) {
        // Lấy tất cả refresh token của user
        List<RefreshToken> userRefreshTokens = refreshTokenRepository.findByUser(user);
        
        // Blacklist từng refresh token
        for (RefreshToken refreshToken : userRefreshTokens) {
            blacklistToken(refreshToken.getRefreshToken(), user, reason);
        }
        
        // Xóa tất cả refresh token sau khi đã blacklist
        refreshTokenRepository.deleteByUser(user);
        
        log.info("Blacklisted {} tokens for user: {} with reason: {}", 
                userRefreshTokens.size(), user.getEmail(), reason);
    }
    
    @Override
    public void cleanupExpiredTokens() {
        // Xóa các token đã blacklist quá 7 ngày (thời gian token hết hạn)
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(7);
        blacklistedTokenRepository.deleteExpiredTokens(cutoffDate);
        log.info("Cleaned up expired blacklisted tokens before: {}", cutoffDate);
    }
    
    private String hashToken(String token) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(token.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
}