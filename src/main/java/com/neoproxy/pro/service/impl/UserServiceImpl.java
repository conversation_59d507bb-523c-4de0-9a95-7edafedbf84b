package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.config.AppConf;
import com.neoproxy.pro.config.Constants;
import com.neoproxy.pro.domain.License;
import com.neoproxy.pro.domain.User;
import com.neoproxy.pro.dto.ExceptionDetail;
import com.neoproxy.pro.dto.NewUserRequest;
import com.neoproxy.pro.dto.ResetPasswordRequest;
import com.neoproxy.pro.dto.UserDto;
import com.neoproxy.pro.enums.*;
import com.neoproxy.pro.mail.EmailDetails;
import com.neoproxy.pro.mail.EmailService;
import com.neoproxy.pro.mapper.UserMapper;
import com.neoproxy.pro.mapper.UserMapperImpl;
import com.neoproxy.pro.repository.RefreshTokenRepository;
import com.neoproxy.pro.repository.UserRepository;
import com.neoproxy.pro.service.*;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.utils.CommonUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.validator.routines.EmailValidator;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class UserServiceImpl implements UserService {
    AppConf appConf;
    UserMapper userMapper = new UserMapperImpl();
    PasswordEncoder passwordEncoder;
    UserRepository userRepository;
    AuthenticationService authenticationService;
    LicenseService licenseService;
    EmailService emailService;
    MonitorService monitorService;
    BlacklistedTokenService blacklistedTokenService;

    public UserServiceImpl(
            AppConf appConf,
            @Lazy PasswordEncoder passwordEncoder,
            UserRepository userRepository,
            AuthenticationService authenticationService,
            LicenseService licenseService,
            EmailService emailService,
            MonitorService monitorService,
            BlacklistedTokenService blacklistedTokenService,
            RefreshTokenRepository refreshTokenRepository) {
        this.appConf = appConf;
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.authenticationService = authenticationService;
        this.licenseService = licenseService;
        this.emailService = emailService;
        this.monitorService = monitorService;
        this.blacklistedTokenService = blacklistedTokenService;
    }

    @Override
    @Transactional
    public User createNewUser(NewUserRequest newUserRequest) {
        Optional<User> userOptional = userRepository.findByEmail(newUserRequest.getEmail());
        if (userOptional.isPresent())
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.USER_EXISTED)
                            .message("The email has been registered. Please use another one.")
                            .build());

        userOptional = userRepository.findByUserName(newUserRequest.getUserName());
        if (userOptional.isPresent())
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.USER_EXISTED)
                            .message("The userName has been registered. Please use another one.")
                            .build());

        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String code = CommonUtil.generatingRandomString();
        User user =
                User.builder()
                        .userName("user" + generateLastName())
                        .name(newUserRequest.getName())
                        .email(newUserRequest.getEmail())
                        .password(passwordEncoder.encode(newUserRequest.getPassword()))
                        .balance(BigDecimal.ZERO)
                        .status(UserStatus.PENDING)
                        .role(UserRole.CLIENT)
                        .transferContent(code)
                        .phoneNumber(newUserRequest.getPhone())
                        .invitor(CommonUtil.isEmpty(newUserRequest.getAffiliateCode()) ? null : newUserRequest.getAffiliateCode())
                        .reminder(1)
                        .build();

        log.debug("Creating new user: {}", user);

        if (appConf.getVerify_email())
            verifyEmail(newUserRequest, code);
        else
            user.setStatus(UserStatus.ACTIVE);

        userRepository.save(user);

        notificationUserSignUp(user);

        return user;
    }


    private void verifyEmail(NewUserRequest newUserRequest, String code) {
        String emailBody = "\n" +
                "Hi " + newUserRequest.getName() + ",\n\n" +
                "Thank you for registering at " + appConf.getName() + "\n\n" +
                "Please click the link below to activate your account. If the page is not visible, you can copy and paste the link into your browser.\n" +
                "\n\n" +
                "  " + appConf.getUrl() + "/register-success?code=" + code +
                "\n\n" +
                "---------------------------\n" +
                "Best regards,\n" +
                appConf.getName();

        EmailDetails emailDetails = EmailDetails.builder()
                .subject(appConf.getName() + " - Verify your email address")
                .msgBody(emailBody)
                .recipient(newUserRequest.getEmail())
                .build();
        String result = emailService.sendSimpleMail(emailDetails);
        log.info("SEND EMAIL: {}", result);
    }

    private void notificationUserSignUp(User user) {
        String notificationEmail = emailService.getAdminEmail();
        if (!CommonUtil.isEmpty(notificationEmail)) {
            try {
                String finalMail = Constants.EMAIL_SIGN_UP
                        .replace("#NAME", user.getName())
                        .replace("#EMAIL", user.getEmail())
                        .replace("#PHONE_NUMBER", user.getPhoneNumber())
                        .replace("#REFER_ID", user.getAffiliateCode())
                        .replace("#CREATED_DATE", LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));

                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Customer submit register form")
                        .msgBody(finalMail)
                        .recipient(emailService.getAdminEmail())
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }

        monitorService.addAlertMonitor(
               MonitorType.SIGNUP,
                user.getEmail(),
                user.getEmail(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"))
        );
    }

    @Override
    public User getUser(@NonNull UUID userUuid) {
        Optional<User> userOpt = userRepository.findByUuid(userUuid);
        if (userOpt.isEmpty()) throw new IllegalArgumentException("User not found");
        return userOpt.get();
    }

    @Override
    public User updateUserEmail(
            @NonNull UUID userUuid, @NotBlank String email, @NotBlank String password) {
        User user = getUser(userUuid);
        validateEmail(email);

        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        return userRepository.save(user);
    }

    private void validateEmail(String email) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent())
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.EMAIL_ADDRESS_EXISTED)
                            .message("Email address has been registered")
                            .build());

        if (!EmailValidator.getInstance().isValid(email))
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Invalid email format")
                            .build());
    }

    @Override
    public User getUserByEmail(String email) {
        return userRepository
                .findByEmail(email)
                .orElseThrow(
                        () ->
                                new NeoProxyServiceException(
                                        ExceptionDetail.builder()
                                                .status(HttpStatus.NOT_FOUND)
                                                .errorCode(ErrorCode.USER_NOT_FOUND)
                                                .message("User not found")
                                                .build()));
    }

    @Override
    public UserDto resetPassword(ResetPasswordRequest request) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        User user = authenticationService.getLoggedUser();

        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.WRONG_PASSWORD)
                            .message("Wrong password")
                            .build());
        }

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);

        // Invalid tất cả token của user
        blacklistedTokenService.blacklistAllUserTokens(user, "PASSWORD_RESET");

        return userMapper.toDto(user);
    }

    @Override
    public UserDto resetPassword(UUID userUuid, String newPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        Optional<User> userOptional = userRepository.findByUuid(userUuid);
        User user = userOptional.get();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        // Invalid tất cả token của user
        blacklistedTokenService.blacklistAllUserTokens(user, "PASSWORD_RESET");
        
        log.info("Password reset and all tokens invalidated for user: {}", user.getEmail());
        return userMapper.toDto(user);
    }
    
    @Override
    public UserDto changePassword(ResetPasswordRequest request) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        User user = userRepository.findByEmail(request.getEmail()).orElseThrow(() ->
                new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.NOT_FOUND)
                                .errorCode(ErrorCode.USER_NOT_FOUND)
                                .message("User not found")
                                .build()));
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.WRONG_PASSWORD)
                            .message("Wrong password")
                            .build());
        }

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
        
        // Invalid tất cả token của user
        blacklistedTokenService.blacklistAllUserTokens(user, "PASSWORD_CHANGE");
        
        log.info("Password changed and all tokens invalidated for user: {}", user.getEmail());
        return userMapper.toDto(user);
    }

    @Override
    public boolean suspended(UUID uuid) {
        User user = getUser(uuid);
        user.setStatus(UserStatus.SUSPENDED);
        userRepository.save(user);

        licenseService.pendingLicenseByCustomer(uuid);

        // Invalid tất cả token của user
        blacklistedTokenService.blacklistAllUserTokens(user, "suspended");

        return true;
    }

    @Override
    public boolean active(UUID uuid) {
        User user = getUser(uuid);
        user.setStatus(UserStatus.ACTIVE);
        userRepository.save(user);
        return true;
    }

    @Override
    public boolean verifyAccount(String code) {
        Optional<User> userOptional = userRepository.findUserByTransferContent(code);
        if (userOptional.isEmpty()) {
            log.info("Not found user by code: {}", code);
            return false;
        }
        User user = userOptional.get();
        user.setStatus(UserStatus.ACTIVE);
        userRepository.save(user);

        return false;
    }

    private String generateLastName() {
        return String.valueOf(new Random().nextInt(10000, 100_000));
    }
}
