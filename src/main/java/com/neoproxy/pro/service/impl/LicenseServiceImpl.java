package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.config.AppConf;
import com.neoproxy.pro.config.Constants;
import com.neoproxy.pro.domain.Package;
import com.neoproxy.pro.domain.*;
import com.neoproxy.pro.dto.*;
import com.neoproxy.pro.enums.*;
import com.neoproxy.pro.mail.EmailDetails;
import com.neoproxy.pro.mail.EmailService;
import com.neoproxy.pro.mapper.LicenseMapper;
import com.neoproxy.pro.mapper.TransactionMapper;
import com.neoproxy.pro.repository.*;
import com.neoproxy.pro.service.AuthenticationService;
import com.neoproxy.pro.service.LicenseService;
import com.neoproxy.pro.service.ProxyService;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.utils.CommonUtil;
import com.neoproxy.pro.utils.DateUtil;
import com.neoproxy.pro.utils.VNCharacterUtils;
import com.neoproxy.pro.xproxy.model.PortType;
import com.neoproxy.pro.xproxy.model.ProxyInfoReq;
import com.neoproxy.pro.xproxy.model.ResetDataCounterReq;
import com.neoproxy.pro.xproxy.service.XProxyService;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.convert.QueryByExamplePredicateBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.Writer;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class LicenseServiceImpl implements LicenseService {
    AppConf appConf;
    AuthenticationService authenticationService;
    LicenseRepository licenseRepository;
    UserRepository userRepository;
    PackageRepository packageRepository;
    XProxyService xProxyService;
    TransactionRepository transactionRepository;
    ModemRepository modemRepository;
    TrackingRepository trackingRepository;
    LicenseMapper licenseMapper;
    ProxyRepository proxyRepository;
    ProxyService proxyService;
    EmailService emailService;
    TransactionMapper transactionMapper;

    public Specification<License> getSpecAndExample(LicenseQueryRequest request, Example<License> example) {
        return (root, query, builder) -> {
            final List<Predicate> predicates = new ArrayList<>();
            if (!CommonUtil.isEmpty(request.getExpiredDateFrom())) {
                predicates.add(builder.greaterThanOrEqualTo(root.get("expiredDate"), DateUtil.convertIsoStringToDate(request.getExpiredDateFrom())));
            }
            if (!CommonUtil.isEmpty(request.getExpiredDateTo())) {
                predicates.add(builder.lessThanOrEqualTo(root.get("expiredDate"), DateUtil.convertIsoStringToDate(request.getExpiredDateTo())));
            }
            if (!CommonUtil.isEmpty(request.getCreatedDateFrom())) {
                predicates.add(builder.greaterThanOrEqualTo(root.get("createdAt"), DateUtil.convertIsoStringToDate(request.getCreatedDateFrom())));
            }
            if (!CommonUtil.isEmpty(request.getCreatedDateTo())) {
                predicates.add(builder.lessThanOrEqualTo(root.get("createdAt"), DateUtil.convertIsoStringToDate(request.getCreatedDateTo())));
            }
            if (!request.getName().isEmpty()) {
                predicates.add(builder.like(root.get("uuid").as(String.class), "%" + request.getName() + "%"));
            }
            if (!request.getTransactionId().isEmpty()) {
                predicates.add(builder.like(root.get("transaction").get("uuid").as(String.class), "%" + request.getTransactionId() + "%"));
            }

            if (!request.getPublicIp().isEmpty()) {
                Predicate pre1 = builder.like(root.get("proxy").get("publicIp"), "%" + request.getPublicIp() + "%");
                predicates.add(builder.or(pre1));
            }
            if (request.getPort() != -1) {
                Predicate pre1 = builder.equal(root.get("httpProxy").get("sharedPort"), request.getPort());
                Predicate pre2 = builder.equal(root.get("sockProxy").get("sharedPort"), request.getPort());
                predicates.add(builder.or(pre1, pre2));
            }
            if (!request.getAuthUsername().isEmpty()) {
                predicates.add(builder.like(root.get("authUser"), "%" + request.getAuthUsername() + "%"));
            }
            if (!request.getAuthIps().isEmpty()) {
                predicates.add(builder.like(root.get("ipWhitelist"), "%" + request.getAuthIps() + "%"));
            }

            predicates.add(QueryByExamplePredicateBuilder.getPredicate(root, builder, example));
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    @Override
    public TableData<LicenseDto> getLicenses(LicenseQueryRequest request) {
        User user = authenticationService.getLoggedUser();
        License searchLicense = new License();
        if (user.isClient()) {
            searchLicense.setCustomer(user);
        }

        TableData<LicenseDto> agentTable = new TableData<>();
        Sort sortBy = Sort.by(Sort.Direction.ASC, "status").by(Sort.Direction.DESC, "updatedAt");
        Pageable paging = PageRequest.of(request.getPage(), request.getPageSize(), sortBy);

        ExampleMatcher matcher = ExampleMatcher
                .matching();
        if (!request.getStatus().isEmpty()) {
            searchLicense.setStatus(LicenseStatus.valueOf(request.getStatus()));
        }
        if (!request.getModemId().isEmpty()) {
            searchLicense.setHttpProxy(Proxy.builder().modem(Modem.builder().uuid(UUID.fromString(request.getModemId())).build()).build());
        }
        if (!request.getCustomerId().isEmpty()) {
            searchLicense.setCustomer(User.builder().uuid(UUID.fromString(request.getCustomerId())).build());
        }
        if (!request.getLocation().isEmpty()) {
            searchLicense.setLocation(request.getLocation());
        }
        if (!request.getPackageId().isEmpty()) {
            searchLicense.setSalePackage(Package.builder().uuid(UUID.fromString(request.getPackageId())).build());
        }

        Example<License> example = Example.of(searchLicense, matcher);
        Page<License> item = licenseRepository.findAll(getSpecAndExample(request, example), paging);

        item.getContent().forEach(license -> {
            LicenseDto licenseDto = licenseMapper.toDto(license);
            agentTable.getData().add(licenseDto);
        });
        agentTable.setPages(item.getTotalPages());

        return agentTable;
    }

    @Override
    public void writeLicenseToCsv(String customer, Writer writer) {
        Optional<User> user = userRepository.findByUuid(UUID.fromString(customer));
        if (user.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.USER_NOT_FOUND)
                            .message("User not found")
                            .build());
        }

        List<License> licenseList;
        if (user.get().isClient()) {
            licenseList = licenseRepository.findLicensesByUser(UUID.fromString(customer), LicenseStatus.ACTIVE);
        } else {
            licenseList = licenseRepository.findByLicenseStatus(LicenseStatus.ACTIVE);
        }

        try (CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.EXCEL)) {
            csvPrinter.printRecord(
                    "License",
                    "IP:Port:User:Pass",
                    "User:Pass@IP:Port",
                    "Expired Date",
                    "Status",
                    "Port http; port socks5",
                    "Location"
            );
            for (License license : licenseList) {
                Proxy httpProxy = license.getHttpProxy();
                Proxy sockProxy = license.getSockProxy();
                if (httpProxy != null) {
                    String format1 = httpProxy.getHost() + ":" + httpProxy.getSharedPort() + (CommonUtil.isEmpty(httpProxy.getAuthenticationUsers()) ? "" : ":" + httpProxy.getAuthenticationUsers());
                    String format2 = (CommonUtil.isEmpty(httpProxy.getAuthenticationUsers()) ? "" : httpProxy.getAuthenticationUsers() + "@") + httpProxy.getHost() + ":" + httpProxy.getSharedPort();
                    String port = httpProxy.getSharedPort() + ";" + sockProxy.getSharedPort();
                    csvPrinter.printRecord(
                            license.getUuid().toString(),
                            format1,
                            format2,
                            license.getExpiredDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy")),
                            license.getStatus().getName(),
                            port,
                            VNCharacterUtils.removeAccent(license.getLocation())
                    );
                }
            }
        } catch (IOException e) {
            log.error("Error While writing CSV ", e);
        }
    }

    @Override
    public LicenseDto updateLicense(@NonNull UUID uuid, @NonNull LicenseRequest licenseRequest) {
        License neoLicense = licenseRepository.findByUuid(uuid);
        if (neoLicense == null)
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("could not find license with this uuid")
                            .build());

        Package salePackage = packageRepository.findByUuid(licenseRequest.getSalePackageId());

        neoLicense.setStatus(licenseRequest.getStatus());
        neoLicense.setExpiredDate(licenseRequest.getExpiredDate());
        neoLicense.setSalePackage(salePackage);
        neoLicense.setAuthUser(licenseRequest.getAuthUser());
        neoLicense.setIpWhitelist(licenseRequest.getIpWhitelist());

        // Assigned new proxy for the active license
        if (licenseRequest.getStatus().equals(LicenseStatus.ACTIVE) && !neoLicense.getStatus().equals(LicenseStatus.ACTIVE)) {
            List<Proxy> httpProxyList = proxyService.getAvailableProxiesByPackage(salePackage);
            if (httpProxyList.isEmpty()) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.PROXY_NOT_ENOUGH)
                                .message("The quantity of proxy was not enough")
                                .build());
            }

            /**
             * Start Assign new proxy
             */
            // 6. Create license active
            Proxy httpProxy = httpProxyList.get(0);
            // 6.1 Create active license
            String authenticationUsers = neoLicense.getAuthUser();
            String authenticationIps = neoLicense.getIpWhitelist();
            int sockPortMatch = httpProxy.getBrotherPort();
            log.info("_____Brother port of http port {} with sock port: {}", httpProxy.getSharedPort(), sockPortMatch);
            Optional<Proxy> sockProxy = proxyRepository.findBySharedPort(httpProxy.getModem(), sockPortMatch, PortType.SocksV5);

            // 6.2 Create license
            neoLicense.setHttpProxy(httpProxy);
            sockProxy.ifPresent(neoLicense::setSockProxy);
            licenseRepository.save(neoLicense);

            // 6.2 Mark httpProxy used
            List<Integer> resetDataIds = new ArrayList<>();
            httpProxy.setProxyToSale();
            proxyRepository.save(httpProxy);
            proxyService.updateAuthentication(httpProxy, neoLicense.getAuthUser(), neoLicense.getIpWhitelist());
            resetDataIds.add(httpProxy.getXproxyId());

            // 6.4 Mark sockProxy used
            sockProxy.ifPresent((sockP) -> {
                sockP.setProxyToSale();
                proxyRepository.save(sockP);
                proxyService.updateAuthentication(sockP, neoLicense.getAuthUser(), neoLicense.getIpWhitelist());
                resetDataIds.add(sockP.getXproxyId());
            });
            // 6.4 Reset data counter
            xProxyService.resetDataCounter(httpProxy.getModem(), new ResetDataCounterReq(resetDataIds));
            /**
             * End Assign new proxy
             */
        } else if (licenseRequest.getStatus() != LicenseStatus.ACTIVE) {
            log.info("--- Update expired license");
            updateExpiredLicense(neoLicense);
        }

        licenseRepository.save(neoLicense);

        return licenseMapper.toDto(neoLicense);
    }

    @Override
    @Transactional
    public boolean extendByLicenseIds(ExtendLicenseRequest request) {
        User user = authenticationService.getLoggedUser();

        // 1.0 Find by proxy
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses was not found or not active.")
                            .build());
        }

        if (!CommonUtil.isEmpty(request.getPackageId())) {
            Package salePackage = packageRepository.findByUuid(request.getPackageId());
            if (salePackage != null) {
                licenseList.forEach(license -> {
                    license.setSalePackage(salePackage);
                    licenseRepository.save(license);
                });
            }
        }

        // 2.0 Check user balance have enough to extend list license
        double totalAmount = licenseList.stream()
                .mapToDouble(license -> {
                    BigDecimal packagePrice = license.getSalePackage().getPrice();
                    // Add VPN fee if license has vpnType
                    if (license.getVpnType() != null && license.getSalePackage().getVpnFee() != null) {
                        packagePrice = packagePrice.add(license.getSalePackage().getVpnFee());
                    }
                    return packagePrice.doubleValue();
                })
                .sum();
        BigDecimal totalPrice = new BigDecimal(totalAmount);
        if (user.getBalance().compareTo(totalPrice) < 0) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                            .message("Account has insufficient balance")
                            .build());
        }
        // 3. Update new balance after extend
        user.setBalance(user.getBalance().subtract(totalPrice));
        userRepository.save(user);

        // 4. Create EXTEND transation
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(totalPrice)
                .currency("USD")
                .type(TransactionType.EXTEND)
                .status(TransactionStatus.COMPLETED)
                .description("License " + request.getUuids())
                .note("")
                .build();
        transactionRepository.save(transaction);

        // 5. Extend for each license
        request.getUuids().forEach(licenseUuid -> {
            this.extendLicense(licenseUuid);
        });

        return true;
    }

    @Override
    @Transactional
    public boolean expressExtendByLicenseIds(ExpressExtendLicenseRequest request) {
        User user = authenticationService.getLoggedUser();

        // 1.0 Find licenses by UUIDs
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses was not found or not active.")
                            .build());
        }

        // 2.0 Calculate total amount using existing license data
        double totalAmount = licenseList.stream()
                .mapToDouble(license -> {
                    BigDecimal packagePrice = license.getSalePackage().getPrice();

                    // Automatically add VPN fee if license has VPN add-on
                    if (license.getVpnType() != null && license.getSalePackage().getVpnFee() != null) {
                        packagePrice = packagePrice.add(license.getSalePackage().getVpnFee());
                        log.info("License {} has VPN add-on {}, adding VPN fee: {}",
                                license.getUuid(), license.getVpnType(), license.getSalePackage().getVpnFee());
                    }

                    return packagePrice.doubleValue();
                })
                .sum();

        BigDecimal totalPrice = new BigDecimal(totalAmount);

        // 3.0 Check user balance
        if (user.getBalance().compareTo(totalPrice) < 0) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                            .message("Account has insufficient balance for express extend")
                            .build());
        }

        // 4.0 Update user balance
        user.setBalance(user.getBalance().subtract(totalPrice));
        userRepository.save(user);

        // 5.0 Create EXPRESS_EXTEND transaction
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(totalPrice)
                .currency("USD")
                .type(TransactionType.EXTEND)
                .status(TransactionStatus.COMPLETED)
                .description("Express License Extend " + request.getUuids())
                .note("Express extend - VPN fees automatically included for applicable licenses")
                .build();
        transactionRepository.save(transaction);

        // 6.0 Extend each license immediately
        request.getUuids().forEach(licenseUuid -> {
            this.extendLicense(licenseUuid);
            log.info("Express extended license: {}", licenseUuid);
        });

        return true;
    }

    private boolean extendLicense(UUID uuid) {
        License license = licenseRepository.findByUuid(uuid);
        if (license != null) {
            Package salePackage = license.getSalePackage();
            // Determine the expiration time
            LocalDateTime currentDateTime = license.getExpiredDate().isAfter(LocalDateTime.now()) ? license.getExpiredDate() : LocalDateTime.now();
            if (salePackage.getPackageUnit() == PackageUnit.WEEK) {
                license.setExpiredDate(currentDateTime.plusDays(7L * salePackage.getDuration()));
            } else if (salePackage.getPackageUnit() == PackageUnit.MONTH) {
                license.setExpiredDate(currentDateTime.plusMonths(salePackage.getDuration()));
            } else if (salePackage.getPackageUnit() == PackageUnit.MINUTES) {
                license.setExpiredDate(currentDateTime.plusMinutes(salePackage.getDuration()));
            } else {
                license.setExpiredDate(currentDateTime.plusDays(salePackage.getDuration()));
            }
            licenseRepository.save(license);
        }
        return true;
    }

    @Override
    public Integer getTotalActiveLicense(ModemType modemType) {
        return licenseRepository.countLicenseByStatus(LicenseStatus.ACTIVE, modemType);
    }

    @Override
    public Integer getTotalExpiredLicenses(ModemType modemType) {
        LocalDate startDate = LocalDate.now();
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        return licenseRepository.countExpiredLicenseByDate(startTime, endTime, modemType);
    }

    @Override
    public Integer getTotalLicenseByUser() {
        User user = authenticationService.getLoggedUser();
        return licenseRepository.countLicenseByUser(user, LicenseStatus.ACTIVE);
    }

    @Override
    public Integer getTotalExpiredLicensesByUser() {
        User user = authenticationService.getLoggedUser();
        LocalDate startDate = LocalDate.now();
        LocalDateTime startTime = startDate.atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        log.info(startDate + " - " + endTime);
        return licenseRepository.countExpiredLicenseByUser(user, startTime, endTime);
    }

    @Override
    public List<License> getExpiredLicenses() {
        return licenseRepository.getExpiredLicenses(LocalDateTime.now(), LicenseStatus.ACTIVE);
    }

    @Override
    public List<User> getExpiredLicensesNextDay() {
        return licenseRepository.getCustomerHaveExpiredLicenses(
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(2),
                LicenseStatus.ACTIVE).stream().distinct().collect(Collectors.toList());
    }

    @Override
    public boolean updateExpiredLicense(License license) {
        // 1. Update proxy available status
        List<Integer> proxyIds = new ArrayList<>();
        Optional<Proxy> httpProxy = Optional.ofNullable(license.getHttpProxy());
        Optional<Proxy> sockProxy = Optional.ofNullable(license.getSockProxy());

        httpProxy.ifPresent(p -> {
            updateProxyAvailable(proxyIds, p, license.getVpnType());
        });

        sockProxy.ifPresent(p -> {
            updateProxyAvailable(proxyIds, p, license.getVpnType());
        });

        // 2. Update license expired status
        license.setStatus(LicenseStatus.EXPIRED);
        license.setHttpProxy(null);
        license.setSockProxy(null);
        licenseRepository.save(license);

        return true;
    }

    private void updateProxyAvailable(List<Integer> proxyIds, Proxy p, VpnType vpnType) {
        try {
            p.setSaleStatus(ProxySaleStatus.AVAILABLE);
            p.setAuthenticationUsers(null);
            p.setAuthorizationIps(null);
            proxyRepository.save(p);
            proxyIds.add(p.getXproxyId());
            ProxyInfoReq proxyInfoReq = new ProxyInfoReq("", "", proxyIds);
            boolean result = xProxyService.bulkEdit(p.getModem(), proxyInfoReq);
            log.info("Update expired license with proxy {} with status {}", p.getUuid(), result);

            // OFF OLD VPN
            if (!CommonUtil.isEmpty(vpnType)) {
                xProxyService.enableVpn(p.getModem(), p.getXproxyPosition(), vpnType.getXproxyType(), false);
            }
        } catch (Exception e) {
            log.error("Error when update proxy available due to {}", e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean switchToNewModem(SwitchModemRequest request) {
        for (UUID targetLicenseId : request.getLicenseUuids()) {
            log.info("[Swith to new modem]");
            License targetLicense = licenseRepository.findByUuid(targetLicenseId);
            Modem targetModem = modemRepository.findByUuid(request.getModemId());

            // 1.0 Check modemType
            if (!targetModem.getType().equals(targetLicense.getModemType())) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.MODEM_ERROR_CONNECT)
                                .message("Cannot change modem because License belongs to different modem type.")
                                .build());
            }

            // 2.0 Check location of modem
            if (!CommonUtil.isEmpty(targetLicense.getLocation())) {
                if (!targetModem.getLocation().equals(targetLicense.getLocation())) {
                    throw new NeoProxyServiceException(
                            ExceptionDetail.builder()
                                    .status(HttpStatus.BAD_REQUEST)
                                    .errorCode(ErrorCode.MODEM_ERROR_CONNECT)
                                    .message("The modem cannot be changed because the License is in another location, please choose the same modem again or change the location of the License.")
                                    .build());
                }
            }

            // 3.0 Get list available proxy
            List<Proxy> httpProxyList = proxyRepository.findAvailableProxyByModem(request.getModemId(), PortType.HTTP);
            if (httpProxyList.isEmpty()) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                                .message("No active proxy found.")
                                .build());
            }

            // 4.1 Clear authentication for old proxy
            Proxy oldHttpProxy = targetLicense.getHttpProxy();
            if (oldHttpProxy != null) {
                if (oldHttpProxy.getModem().getStatus() == ModemStatus.READY) {
                    String newAuthenReset = CommonUtil.generateAuthProxy();
                    List<Integer> proxyIds = new ArrayList<>();

                    proxyIds.add(oldHttpProxy.getXproxyId());
                    oldHttpProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
                    oldHttpProxy.setAuthenticationUsers(newAuthenReset);
                    oldHttpProxy.setAuthorizationIps(null);
                    proxyRepository.save(oldHttpProxy);
                    targetLicense.setHttpProxy(null);


                    Proxy oldSockProxy = targetLicense.getSockProxy();
                    if (oldSockProxy != null) {
                        proxyIds.add(oldSockProxy.getXproxyId());
                        oldSockProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
                        oldSockProxy.setAuthenticationUsers(newAuthenReset);
                        oldSockProxy.setAuthorizationIps(null);
                        proxyRepository.save(oldSockProxy);
                        targetLicense.setSockProxy(null);
                    }

                    ProxyInfoReq proxyInfoReq = new ProxyInfoReq("", newAuthenReset, proxyIds);
                    xProxyService.bulkEditAsync(oldHttpProxy.getModem(), proxyInfoReq);
                }
            }

            /**
             * Start Assign new proxy
             */
            // 4.2 Update authenticate for new proxy
            List<Integer> resetDataIds = new ArrayList<>();

            Proxy newHttpProxy = httpProxyList.get(0);
            Optional<Proxy> newSockProxy = proxyRepository.findBySharedPort(newHttpProxy.getModem(), newHttpProxy.getBrotherPort(), PortType.SocksV5);

            newHttpProxy.setProxyToSale();
            proxyRepository.save(newHttpProxy);
            proxyService.updateAuthentication(newHttpProxy, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
            targetLicense.setHttpProxy(newHttpProxy);
            resetDataIds.add(newHttpProxy.getXproxyId());

            newSockProxy.ifPresent(p -> {
                p.setProxyToSale();
                proxyRepository.save(p);
                proxyService.updateAuthentication(p, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
                targetLicense.setSockProxy(p);
                resetDataIds.add(p.getXproxyId());
            });

            targetLicense.setLocation(newHttpProxy.getModem().getLocation());
            targetLicense.setLastChangeIp(LocalDateTime.now());
            licenseRepository.save(targetLicense);

            // 6.4 Reset data counter
            xProxyService.resetDataCounter(newHttpProxy.getModem(), new ResetDataCounterReq(resetDataIds));
            /**
             * End Assign new proxy
             */

            sendUpgradeProxyEmail(targetLicense.getCustomer(), targetLicense, oldHttpProxy);
        }


        return true;
    }

    @Override
    public boolean updateLicenseStatus(UpdateLicenseStatusRequest request) {
        for (UUID licenseId : request.getUuids()) {
            License targetLicense = licenseRepository.findByUuid(licenseId);
            if (targetLicense == null)
                continue;
            if (targetLicense.getStatus() == LicenseStatus.EXPIRED) {
                continue;
            }
            if (request.getLicenseStatus().equals(LicenseStatus.EXPIRED)) {
                log.info("--------------- Update expired license");
                updateExpiredLicense(targetLicense);
            } else {
                targetLicense.setStatus(targetLicense.getStatus().equals(LicenseStatus.ACTIVE) ? LicenseStatus.PENDING : LicenseStatus.ACTIVE);
                licenseRepository.save(targetLicense);
            }
        }
        return true;
    }

    @Override
    public void writeTrackingToCsv(UUID license, Writer writer) {
        List<Tracking> trackingList = trackingRepository.findTrackingByLicence(license, LocalDateTime.now().minusDays(30));
        try (CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.EXCEL)) {
            csvPrinter.printRecord(
                    "License",
                    "IP",
                    "UserAgent",
                    "API",
                    "Time"
            );
            for (Tracking tracking : trackingList) {
                csvPrinter.printRecord(
                        tracking.getLicense().toString(),
                        tracking.getIpAddress(),
                        tracking.getUserAgent(),
                        tracking.getApiName(),
                        tracking.getCreatedAt().format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"))
                );
            }
        } catch (IOException e) {
            log.error("Error While writing CSV ", e);
        }
    }

    @Override
    public void pendingLicenseByCustomer(UUID customerId) {
        List<License> licenseList = licenseRepository.findLicensesByUser(customerId, LicenseStatus.ACTIVE);
        licenseList.forEach(license -> {
            pendingLicense(license);
        });
    }

    public boolean pendingLicense(License license) {
        Proxy httpProxy = license.getHttpProxy();
        Proxy sockProxy = license.getSockProxy();
        if (httpProxy != null && sockProxy != null) {
            updateExpiredLicense(license);
        }

        license.setStatus(LicenseStatus.PENDING);
        licenseRepository.save(license);
        return true;
    }

    @Override
    public boolean changeRotationTime(ChangeRotationTimeRequest request) {
        User user = authenticationService.getLoggedUser();
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses was not found or not active.")
                            .build());
        }

        licenseList.forEach(license -> {
            license.setAutoRotationTime(request.getAutoRotationTime());
            licenseRepository.save(license);
        });
        return true;
    }

    @Override
    @Transactional
    public boolean changeAvailableProxy(LicenseChangeProxyRequest request) {
        User user = authenticationService.getLoggedUser();
        if (user.isClient()) {
            return false;
        }

        List<License> licenseList = licenseRepository.getLicenseByUuids(request.getUuids());
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses was not found.")
                            .build());
        }

        Proxy newHttpProxy = proxyRepository.findByUuid(request.getProxyId());
        if (CommonUtil.isEmpty(newHttpProxy) || newHttpProxy.getLicense() != null || newHttpProxy.getLicenseSock() != null) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("The proxy was not available! Or be used by the other license!!!")
                            .build());
        }

        License targetLicense = licenseList.get(0);
        String newAuthenReset = CommonUtil.generateAuthProxy();
        List<Integer> proxyIds = new ArrayList<>();

        Proxy oldHttpProxy = targetLicense.getHttpProxy();
        if (oldHttpProxy != null) {
            proxyIds.add(oldHttpProxy.getXproxyId());
            oldHttpProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
            oldHttpProxy.setAuthenticationUsers(newAuthenReset);
            oldHttpProxy.setAuthorizationIps(null);
            proxyRepository.save(oldHttpProxy);
            targetLicense.setHttpProxy(null);
        }

        Proxy oldSockProxy = targetLicense.getSockProxy();
        if (oldSockProxy != null) {
            proxyIds.add(oldSockProxy.getXproxyId());
            oldSockProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
            oldSockProxy.setAuthenticationUsers(newAuthenReset);
            oldSockProxy.setAuthorizationIps(null);
            proxyRepository.save(oldSockProxy);
            targetLicense.setSockProxy(null);
        }

        if (!proxyIds.isEmpty()) {
            ProxyInfoReq proxyInfoReq = new ProxyInfoReq("", newAuthenReset, proxyIds);
            boolean result = xProxyService.bulkEdit(oldHttpProxy.getModem(), proxyInfoReq);
            log.info("Done reset old proxy with result {}", result);
            /* Do next action when error xproxy
            if (!result) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.BAD_DATA)
                                .message("The xproxy server cannot connect. Please try later!!!")
                                .build());
            }*/
        }

        // 4.2 Update authenticate for new proxy
        Optional<Proxy> newSockProxy = proxyRepository.findBySharedPort(newHttpProxy.getModem(), newHttpProxy.getBrotherPort(), PortType.SocksV5);

        List<Integer> resetDataIds = new ArrayList<>();
        newHttpProxy.setProxyToSale();
        resetDataIds.add(newHttpProxy.getXproxyId());
        proxyRepository.save(newHttpProxy);
        proxyService.updateAuthentication(newHttpProxy, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
        targetLicense.setHttpProxy(newHttpProxy);

        newSockProxy.ifPresent(sockP -> {
            sockP.setProxyToSale();
            proxyRepository.save(sockP);
            proxyService.updateAuthentication(sockP, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
            targetLicense.setSockProxy(sockP);
            resetDataIds.add(sockP.getXproxyId());
        });

        targetLicense.setLocation(newHttpProxy.getModem().getLocation());
        targetLicense.setLastChangeIp(LocalDateTime.now());
        licenseRepository.save(targetLicense);

        // Reset data counter when change proxy
        xProxyService.resetDataCounter(newHttpProxy.getModem(), new ResetDataCounterReq(resetDataIds));

        // Send email
        sendUpgradeProxyEmail(targetLicense.getCustomer(), targetLicense, oldHttpProxy);

        return true;
    }

    @Async
    void sendUpgradeProxyEmail(User user, License license, Proxy oldProxy) {
        if (!CommonUtil.isEmpty(user.getEmail()) && user.getReminder().equals(1)) {
            try {
                String finalMail = Constants.EMAIL_CHANGE_PROXY;
                finalMail = finalMail.replace("#NAME", user.getName());
                Proxy httpProxy = license.getHttpProxy();
                finalMail = finalMail.replace("#HOST_OF_PROXY", httpProxy.getHost())
                        .replace("#PORT_OF_PROXY", httpProxy.getSharedPort().toString())
                        .replace("#AUTHENTICATE", !CommonUtil.isEmpty(license.getAuthUser()) ? license.getAuthUser() : license.getIpWhitelist())
                        .replace("#START_DATE", license.getStartDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy")))
                        .replace("#END_DATE", license.getExpiredDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy")));

                finalMail = finalMail.replace("#HOST_OF_OLD_PROXY", oldProxy.getHost())
                        .replace("#PORT_OF_OLD_PROXY", oldProxy.getSharedPort().toString());

                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Upgrade the device")
                        .msgBody(finalMail)
                        .recipient(user.getEmail())
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }
    }

    @Override
    @Transactional
    public boolean toggleAutoRenewal(ToggleAutoRenewalRequest request) {
        User user = authenticationService.getLoggedUser();

        // 1.0 Find licenses by UUIDs and ensure they belong to the current user
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses were not found or not active.")
                            .build());
        }

        // 2.0 Update autoRenewal status for all licenses
        licenseList.forEach(license -> {
            license.setAutoRenewal(request.getAutoRenewal());
            licenseRepository.save(license);
            log.info("Updated autoRenewal to {} for license: {}", request.getAutoRenewal(), license.getUuid());
        });

        log.info("Successfully toggled autoRenewal to {} for {} licenses", request.getAutoRenewal(), licenseList.size());
        return true;
    }


    @Async
    private void sendPurchaseVpnEmail(User customer, List<License> licenses, VpnType vpnType, BigDecimal totalFee) {
        try {
            String subject = "VPN Purchase Confirmation";
            StringBuilder licenseDetails = new StringBuilder();

            for (License license : licenses) {
                licenseDetails.append(String.format(
                        "• License ID: %s\n" +
                                "  Plan: %s\n" +
                                "  Location: %s\n" +
                                "  VPN Type: %s\n\n",
                        license.getUuid(),
                        license.getSalePackage().getName(),
                        license.getLocation(),
                        vpnType.getName()
                ));
            }

            String body = String.format(
                    "Dear %s,\n\n" +
                            "Your VPN purchase has been completed successfully!\n\n" +
                            "Purchase Details:\n\n" +
                            "%s" +
                            "Total Amount: $%.2f\n" +
                            "Current Balance: $%.2f\n\n" +
                            "You can now download your VPN configuration files from your dashboard.\n\n" +
                            "If you have any questions or need assistance, please don't hesitate to contact our support team:\n" +
                            "• Email: <EMAIL>\n" +
                            "• Live Chat: Available 24/7 on our website\n\n" +
                            "Thank you for choosing NeoProxy!\n\n" +
                            "Best regards,\n" +
                            "The NeoProxy Team",
                    customer.getName(),
                    licenseDetails.toString(),
                    totalFee,
                    customer.getBalance()
            );

            EmailDetails emailDetails = EmailDetails.builder()
                    .subject(subject)
                    .msgBody(body)
                    .recipient(customer.getEmail())
                    .build();

            String result = emailService.sendSimpleMail(emailDetails);
            log.info("VPN purchase email sent successfully: {}", result);
        } catch (Exception e) {
            log.error("Failed to send VPN purchase email", e);
        }
    }

    @Override
    @Transactional
    public boolean cancelVpn(CancelVpnRequest request) {
        User user = authenticationService.getLoggedUser();

        // 1.0 Find licenses by UUIDs and ensure they belong to the current user
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getLicenseUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses were not found or not active.")
                            .build());
        }

        // 2.0 Check if licenses have VPN enabled
        List<License> licensesWithoutVpn = licenseList.stream()
                .filter(license -> license.getVpnType() == null)
                .collect(Collectors.toList());
        if (!licensesWithoutVpn.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Some licenses do not have VPN enabled.")
                            .build());
        }

        // 3.0 Disable VPN for each license
        List<String> canceledLicenses = new ArrayList<>();
        licenseList.forEach(license -> {
            try {
                // Disable VPN on XProxy server
                xProxyService.enableVpn(
                        license.getHttpProxy().getModem(),
                        license.getHttpProxy().getXproxyPosition(),
                        license.getVpnType().getXproxyType(),
                        false // Disable VPN
                );

                // Remove VPN type from license
                VpnType previousVpnType = license.getVpnType();
                license.setVpnType(null);
                licenseRepository.save(license);

                canceledLicenses.add(license.getUuid().toString());
                log.info("Successfully canceled VPN {} for license: {}", previousVpnType, license.getUuid());
            } catch (Exception e) {
                log.error("Failed to cancel VPN for license: {}", license.getUuid(), e);
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .errorCode(ErrorCode.BAD_DATA)
                                .message("Failed to cancel VPN for license: " + license.getUuid())
                                .build());
            }
        });

        // 4.0 Send cancellation email
        sendCancelVpnEmail(user, licenseList);

        log.info("Successfully canceled VPN for {} licenses: {}",
                licenseList.size(), String.join(",", canceledLicenses));

        return true;
    }

    @Async
    private void sendCancelVpnEmail(User customer, List<License> licenses) {
        try {
            String subject = "VPN Cancellation Confirmation";
            StringBuilder licenseDetails = new StringBuilder();

            for (License license : licenses) {
                licenseDetails.append(String.format(
                        "• License ID: %s\n" +
                                "  Plan: %s\n" +
                                "  Location: %s\n\n",
                        license.getUuid(),
                        license.getSalePackage().getName(),
                        license.getLocation()
                ));
            }

            String body = String.format(
                    "Dear %s,\n\n" +
                            "Your VPN service has been successfully canceled for the following licenses:\n\n" +
                            "%s" +
                            "VPN access has been disabled and you will no longer be able to download VPN configuration files for these licenses.\n\n" +
                            "Your proxy service will continue to work normally without VPN functionality.\n\n" +
                            "If you wish to re-enable VPN service in the future, you can purchase it again from your dashboard.\n\n" +
                            "If you have any questions or need assistance, please don't hesitate to contact our support team:\n" +
                            "• Email: <EMAIL>\n" +
                            "• Live Chat: Available 24/7 on our website\n\n" +
                            "Thank you for choosing NeoProxy!\n\n" +
                            "Best regards,\n" +
                            "The NeoProxy Team",
                    customer.getName(),
                    licenseDetails.toString()
            );

            EmailDetails emailDetails = EmailDetails.builder()
                    .subject(subject)
                    .msgBody(body)
                    .recipient(customer.getEmail())
                    .build();

            String result = emailService.sendSimpleMail(emailDetails);
            log.info("VPN cancellation email sent successfully: {}", result);
        } catch (Exception e) {
            log.error("Failed to send VPN cancellation email", e);
        }
    }

    @Override
    @Transactional
    public TransactionDto purchaseVpn(PurchaseVpnRequest request) {
        User user = authenticationService.getLoggedUser();

        // 1.0 Find licenses by UUIDs and ensure they belong to the current user
        List<License> licenseList = licenseRepository.getLicenseByUuids(user, request.getLicenseUuids(), LicenseStatus.ACTIVE);
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LICENSE_ACTIVE_NOT_FOUND)
                            .message("The licenses were not found or not active.")
                            .build());
        }

        // 2.0 Check if licenses already have VPN
        List<License> licensesWithVpn = licenseList.stream()
                .filter(license -> license.getVpnType() != null)
                .collect(Collectors.toList());
        if (!licensesWithVpn.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Some licenses already have VPN enabled.")
                            .build());
        }

        // 3.0 Calculate total VPN fee
        BigDecimal totalVpnFee = licenseList.stream()
                .map(license -> {
                    Package salePackage = license.getSalePackage();
                    if (salePackage.getVpnFee() == null) {
                        throw new NeoProxyServiceException(
                                ExceptionDetail.builder()
                                        .status(HttpStatus.BAD_REQUEST)
                                        .errorCode(ErrorCode.BAD_DATA)
                                        .message("VPN fee is not configured for this package.")
                                        .build());
                    }
                    return salePackage.getVpnFee();
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 4.0 Check user balance
        if (user.getBalance().compareTo(totalVpnFee) < 0) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                            .message("Insufficient balance to purchase VPN.")
                            .build());
        }

        // 5.0 Create transaction
        List<String> transactionContent = new ArrayList<>();
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(totalVpnFee)
                .currency("USD")
                .type(TransactionType.PURCHASE)
                .status(TransactionStatus.COMPLETED)
                .description("")
                .note("VPN Purchase")
                .discount(BigDecimal.ZERO)
                .build();
        transactionRepository.save(transaction);

        // 6.0 Enable VPN for each license
        licenseList.forEach(license -> {
            try {
                // Enable VPN on XProxy server
                xProxyService.enableVpn(
                        license.getHttpProxy().getModem(),
                        license.getHttpProxy().getXproxyPosition(),
                        request.getVpnType().getXproxyType(),
                        true
                );

                // Update license with VPN type
                license.setVpnType(request.getVpnType());
                licenseRepository.save(license);

                transactionContent.add(license.getUuid().toString());
                log.info("Successfully enabled VPN {} for license: {}", request.getVpnType(), license.getUuid());
            } catch (Exception e) {
                log.error("Failed to enable VPN for license: {}", license.getUuid(), e);
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .errorCode(ErrorCode.BAD_DATA)
                                .message("Failed to enable VPN for license: " + license.getUuid())
                                .build());
            }
        });

        // 7.0 Update transaction description and user balance
        transaction.setDescription("VPN Purchase for licenses: " + String.join(",", transactionContent));
        transactionRepository.save(transaction);

        user.setBalance(user.getBalance().subtract(totalVpnFee));
        userRepository.save(user);

        // 8.0 Send purchase VPN email
        sendPurchaseVpnEmail(user, licenseList, request.getVpnType(), totalVpnFee);

        log.info("Successfully purchased VPN {} for {} licenses. Total fee: {}",
                request.getVpnType(), licenseList.size(), totalVpnFee);

        return transactionMapper.toDto(transaction);
    }


    public void updateTcpOS(List<Integer> proxyIds, Proxy p, String tcpOS) {
        try {
            proxyIds.add(p.getXproxyId());
            ProxyInfoReq proxyInfoReq = new ProxyInfoReq(proxyIds, tcpOS);
            boolean result = xProxyService.bulkEdit(p.getModem(), proxyInfoReq);
            log.info("Update tcpOS with proxy {} - tcpOS {} - result {}", p.getUuid(), tcpOS, result);
        } catch (Exception e) {
            log.error("Error when update tcpOS due to {}", e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateTcpOSForLicenses(UpdateTcpOSRequest request) {
        User user = authenticationService.getLoggedUser();
        
        // Tìm các license thuộc về user hiện tại
        List<License> licenses = licenseRepository.getLicenseByUuids(user, request.getLicenseUuids(), LicenseStatus.ACTIVE);
        
        if (licenses.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("No valid licenses found for the current user")
                            .build());
        }
        
        // Group licenses by modem để optimize việc gọi API
        for (License license : licenses) {
            if (license.getStatus() == LicenseStatus.ACTIVE) {
                List<Integer> proxyIds = new ArrayList<>();
                
                // Collect HTTP proxy
                if (license.getHttpProxy() != null) {
                    updateTcpOS(proxyIds, license.getHttpProxy(), request.getTcpOS());
                }
                
                // Collect SOCK proxy
                if (license.getSockProxy() != null) {
                    updateTcpOS(proxyIds, license.getSockProxy(), request.getTcpOS());
                }
                
                // Cập nhật thông tin tcpOS vào license
                license.setTcpOS(request.getTcpOS());
                licenseRepository.save(license);
                log.info("Updated tcpOS to {} for license: {}", request.getTcpOS(), license.getUuid());
            }
        }
        
        return true;
    }

}
