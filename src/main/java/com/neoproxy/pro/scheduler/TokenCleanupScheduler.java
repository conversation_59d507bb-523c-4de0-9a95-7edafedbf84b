package com.neoproxy.pro.scheduler;

import com.neoproxy.pro.service.BlacklistedTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class TokenCleanupScheduler {
    
    private final BlacklistedTokenService blacklistedTokenService;
    
    // Chạy mỗi ngày lúc 2:00 AM để cleanup token hết hạn
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTokens() {
        log.info("Starting cleanup of expired blacklisted tokens");
        blacklistedTokenService.cleanupExpiredTokens();
        log.info("Completed cleanup of expired blacklisted tokens");
    }
}