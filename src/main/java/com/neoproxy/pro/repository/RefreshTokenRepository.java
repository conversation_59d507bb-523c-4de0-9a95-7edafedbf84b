package com.neoproxy.pro.repository;

import com.neoproxy.pro.domain.RefreshToken;
import com.neoproxy.pro.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {
    
    Optional<RefreshToken> findByRefreshToken(String refreshToken);
    
    List<RefreshToken> findByUser(User user); // Thêm method này
    
    @Modifying
    @Transactional
    void deleteByUser(User user);
}
