package com.neoproxy.pro.repository;

import com.neoproxy.pro.domain.BlacklistedToken;
import com.neoproxy.pro.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.time.LocalDateTime;

@Repository
public interface BlacklistedTokenRepository extends JpaRepository<BlacklistedToken, Long> {
    
    boolean existsByTokenHash(String tokenHash);
    
    @Modifying
    @Transactional
    @Query("DELETE FROM BlacklistedToken bt WHERE bt.blacklistedAt < :cutoffDate")
    void deleteExpiredTokens(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Modifying
    @Transactional
    void deleteByUser(User user);
}