CREATE TABLE IF NOT EXISTS blacklisted_tokens
(
    id                              BIGSERIAL PRIMARY KEY   NOT NULL,
    token_hash                      VARCHAR(255)            NOT NULL,
    user_id                         BIGINT                  NOT NULL,
    blacklisted_at                  TIMESTAMP               NOT NULL,
    reason                          VARCHAR(100)            NOT NULL,
    UNIQUE (token_hash),
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id)
);

CREATE INDEX idx_blacklisted_tokens_token_hash ON blacklisted_tokens(token_hash);
CREATE INDEX idx_blacklisted_tokens_user_id ON blacklisted_tokens(user_id);