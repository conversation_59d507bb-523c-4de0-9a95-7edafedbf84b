2025-09-05 14:13:56.545 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-09-05 14:13:56.546 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-09-05 14:13:56.550 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 127839 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-09-05 14:13:56.551 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-09-05 14:13:56.944 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-05 14:13:56.998 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 14 JPA repository interfaces.
2025-09-05 14:13:57.275 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-05 14:13:57.279 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-05 14:13:57.280 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-05 14:13:57.280 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-09-05 14:13:57.319 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-09-05 14:13:57.319 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 748 ms
2025-09-05 14:13:57.343 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-09-05 14:13:57.427 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-09-05 14:13:57.462 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-09-05 14:13:57.463 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-09-05 14:13:57.464 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-09-05 14:13:57.464 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-09-05 14:13:57.465 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-09-05 14:13:57.466 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-09-05 14:13:57.467 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................**************************************************************************************
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-09-05 14:13:57.468 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"proxydb"
2025-09-05 14:13:57.469 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-09-05 14:13:57.469 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-05 14:14:01.256 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@d504137
2025-09-05 14:14:01.257 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-05 14:14:01.357 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=1, idle=0, waiting=0)
2025-09-05 14:14:01.800 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-09-05 14:14:01.800 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ************************************************************************************** (PostgreSQL 13.15)
2025-09-05 14:14:01.800 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-09-05 14:14:01.801 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-09-05 14:14:01.802 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-09-05 14:14:01.802 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-09-05 14:14:04.051 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@70e343eb
2025-09-05 14:14:04.321 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-09-05 14:14:04.591 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-09-05 14:14:04.600 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-09-05 14:14:04.601 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-09-05 14:14:05.708 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 39 migrations (execution time 00:01.116s)
2025-09-05 14:14:06.736 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@eec6012
2025-09-05 14:14:07.565 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 44
2025-09-05 14:14:07.566 [[main]] WARN  o.f.core.internal.command.DbMigrate - Schema "public" has a version (44) that is newer than the latest available migration (42) !
2025-09-05 14:14:07.831 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-09-05 14:14:08.759 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 65 of 136M
2025-09-05 14:14:08.791 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-05 14:14:08.807 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-09-05 14:14:08.859 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-05 14:14:08.895 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-09-05 14:14:09.332 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2d90cea4
2025-09-05 14:14:09.916 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-05 14:14:09.919 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-05 14:14:10.386 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-05 14:14:10.561 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-09-05 14:14:10.561 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-09-05 14:14:10.561 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-09-05 14:14:10.561 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-09-05 14:14:10.575 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@313c3cb, org.springframework.security.web.context.SecurityContextPersistenceFilter@25d6ae3, org.springframework.security.web.header.HeaderWriterFilter@4b4a3114, org.springframework.security.web.authentication.logout.LogoutFilter@301434fb, com.neoproxy.pro.config.JwtRequestFilter@5a0f5567, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@56d189d0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2823bd30, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63fef83c, org.springframework.security.web.session.SessionManagementFilter@6e98fd10, org.springframework.security.web.access.ExceptionTranslationFilter@7b88a2e2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@17c3e33]
2025-09-05 14:14:10.668 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-09-05 14:14:10.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-09-05 14:14:10.670 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-09-05 14:14:10.671 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-09-05 14:14:10.671 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-09-05 14:14:10.673 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{GET [/v1/users/me]}: getLoggedUser()
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-09-05 14:14:10.674 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
2025-09-05 14:14:10.674 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
2025-09-05 14:14:10.675 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-09-05 14:14:10.676 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-09-05 14:14:10.677 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-09-05 14:14:10.677 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-09-05 14:14:10.677 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-09-05 14:14:10.678 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-09-05 14:14:10.678 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
2025-09-05 14:14:10.678 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-09-05 14:14:10.678 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-09-05 14:14:10.679 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-09-05 14:14:10.679 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-09-05 14:14:10.680 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-09-05 14:14:10.680 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
2025-09-05 14:14:10.680 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-09-05 14:14:10.680 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-09-05 14:14:10.681 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
2025-09-05 14:14:10.681 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-09-05 14:14:10.682 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-09-05 14:14:10.687 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-09-05 14:14:10.695 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-09-05 14:14:10.704 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-09-05 14:14:10.835 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-09-05 14:14:10.855 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-05 14:14:10.861 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-09-05 14:14:10.873 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 14.487 seconds (JVM running for 14.783)
2025-09-05 14:14:12.272 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1ddabc62
2025-09-05 14:14:15.182 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3694a493
2025-09-05 14:14:17.837 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3e8ba47f
2025-09-05 14:14:20.500 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@12856c50
2025-09-05 14:14:23.148 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@71ce9a23
2025-09-05 14:14:25.789 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1df2e32c
2025-09-05 14:14:25.790 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:14:31.358 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:14:31.358 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:15:01.359 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:15:01.359 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:15:31.360 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:15:31.360 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:15:41.027 [[http-nio-8080-exec-1]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-05 14:15:41.027 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-05 14:15:41.028 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-09-05 14:16:01.361 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:16:01.361 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:16:31.362 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:16:31.362 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:17:01.362 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-09-05 14:17:01.362 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:17:31.363 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:17:31.363 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:18:01.364 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:18:01.364 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:18:31.365 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:18:31.365 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:19:01.366 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:19:01.366 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:19:31.366 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:19:31.366 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:20:01.367 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:20:01.367 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:20:31.367 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:20:31.368 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:21:01.368 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:21:01.369 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:21:31.369 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:21:31.369 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:22:01.369 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:22:01.370 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:22:31.370 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:22:31.371 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-09-05 14:23:01.371 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-09-05 14:23:01.372 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
